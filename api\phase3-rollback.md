# 第三阶段升级回滚方案

## 📋 回滚概述

**回滚目标**: Spring Boot 2.7.18 + Java 17 → Spring Boot 2.3.12 + Java 8  
**预计回滚时间**: 5分钟内  
**适用场景**: 第三阶段升级失败或出现严重问题时  

## 🚨 回滚触发条件

以下情况需要立即执行回滚：
- [ ] 应用无法正常启动
- [ ] 核心业务功能异常
- [ ] 性能严重下降（超过20%）
- [ ] 出现数据安全问题
- [ ] Java 17兼容性问题

## 🔄 快速回滚步骤

### 1. 停止当前应用
```bash
# 查找并停止Java进程
jps -l | grep jeecg
kill -9 <进程ID>

# 或者使用pkill
pkill -f "jeecg-boot-module-system"
```

### 2. 回滚POM文件
```bash
# 恢复Spring Boot版本
cd api
git checkout HEAD~1 -- pom.xml

# 或者手动修改pom.xml
# 将Spring Boot版本从2.7.18改回2.3.12.RELEASE
# 将Java版本从17改回1.8
```

### 3. 回滚配置文件
```bash
# 恢复application.yml配置
git checkout HEAD~1 -- jeecg-boot-module-system/src/main/resources/application*.yml

# 删除Spring Boot 2.7特有配置
# 恢复原有的配置结构
```

### 4. 切换Java版本
```bash
# Windows环境
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证Java版本
java -version
# 应该显示: java version "1.8.0_XXX"
```

### 5. 重新编译
```bash
# 清理并重新编译
mvn clean compile -DskipTests

# 如果编译失败，强制清理
mvn clean install -DskipTests -U
```

### 6. 启动应用（Java 8模式）
```bash
# 使用Java 8启动参数
java -Xms1024m -Xmx2048m -XX:+UseG1GC \
     -jar jeecg-boot-module-system/target/teaching-open-2.7.0.jar \
     --spring.profiles.active=dev
```

### 7. 验证回滚成功
```bash
# 检查应用状态
curl -f http://localhost:8081/api/actuator/health

# 检查Java版本
jps -v | grep jeecg

# 测试核心功能
curl -X GET "http://localhost:8081/api/sys/common/403"
```

## 📋 回滚验证清单

### 应用层面
- [ ] 应用正常启动（30秒内）
- [ ] 端口8081正常监听
- [ ] 健康检查接口返回正常
- [ ] 日志无ERROR级别错误

### 功能层面
- [ ] 系统登录功能正常
- [ ] 数据库连接正常
- [ ] Redis连接正常（如有）
- [ ] 核心业务接口正常

### 性能层面
- [ ] 启动时间恢复到升级前水平
- [ ] 内存使用正常
- [ ] 响应时间正常

## 🔧 回滚后处理

### 1. 问题分析
- 记录升级失败的具体错误信息
- 分析失败原因（依赖冲突、配置问题、兼容性问题）
- 制定改进方案

### 2. 环境清理
```bash
# 清理临时文件
rm -rf target/
rm -rf logs/upgrade-*.log

# 重置Git状态（如需要）
git reset --hard HEAD~1
```

### 3. 通知相关人员
- 通知项目负责人回滚完成
- 记录回滚原因和时间
- 安排后续升级计划

## ⚠️ 注意事项

### 数据安全
- 回滚前确保数据库数据完整性
- 如有数据变更，需要评估数据兼容性
- 必要时恢复数据库备份

### 版本控制
- 使用Git管理代码版本
- 回滚后创建回滚记录分支
- 保留升级尝试的代码用于后续分析

### 业务连续性
- 选择业务低峰期执行回滚
- 提前通知用户可能的服务中断
- 确保回滚后业务功能完整

## 📞 应急联系

**技术负责人**: [联系方式]  
**项目经理**: [联系方式]  
**运维人员**: [联系方式]  

---

**重要提醒**: 回滚操作需要谨慎执行，确保每个步骤都正确完成。如有疑问，立即联系技术负责人。
