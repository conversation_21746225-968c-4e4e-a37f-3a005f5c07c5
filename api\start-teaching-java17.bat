@echo off
chcp 65001 >nul
echo 🚀 Teaching系统 Java 17 启动脚本
echo 阶段三：核心框架升级 - Java 17优化启动参数
echo ============================================================

:: 项目配置
set PROJECT_NAME=teaching-system
set PROJECT_JAR=jeecg-boot-module-system\target\teaching-open-2.7.0.jar
set PROFILE=dev

:: Java 17优化的JVM启动参数
set JAVA_OPTS=-Xms2048m -Xmx4096m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -Xlog:gc*:logs/gc.log:time,tags -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/heapdump.hprof --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED

:: 创建日志目录
if not exist logs mkdir logs

:: 检查Java版本
echo.
echo 🔍 检查Java版本...
java -version 2>&1 | findstr "version" > temp_version.txt
set /p JAVA_VERSION_LINE=<temp_version.txt
del temp_version.txt
echo 当前Java版本: %JAVA_VERSION_LINE%

:: 简单检查是否包含17
echo %JAVA_VERSION_LINE% | findstr "17" >nul
if errorlevel 1 (
    echo ❌ 错误: 需要Java 17
    echo 请设置JAVA_HOME指向Java 17安装目录
    pause
    exit /b 1
)

:: 检查JAR文件是否存在
if not exist "%PROJECT_JAR%" (
    echo ❌ 错误: JAR文件不存在: %PROJECT_JAR%
    echo 请先执行: mvn clean package
    pause
    exit /b 1
)

:: 停止现有进程
echo.
echo 🛑 停止现有进程...
for /f "tokens=1" %%i in ('jps -l ^| findstr jeecg-boot-module-system') do (
    echo 发现现有进程 PID: %%i，正在停止...
    taskkill /F /PID %%i >nul 2>&1
)
timeout /t 3 /nobreak >nul

:: 启动应用
echo.
echo 🚀 启动Teaching系统 (Java 17模式)...
echo 配置文件: %PROFILE%
echo JAR文件: %PROJECT_JAR%
echo 启动时间: %date% %time%
echo.

start /b java %JAVA_OPTS% -jar %PROJECT_JAR% --spring.profiles.active=%PROFILE% > teaching-api.log 2>&1

:: 等待进程启动
timeout /t 5 /nobreak >nul

:: 获取新进程PID
for /f "tokens=1" %%i in ('jps -l ^| findstr jeecg-boot-module-system') do set NEW_PID=%%i

if defined NEW_PID (
    echo ✅ 启动成功! PID: %NEW_PID%
    echo.
    echo 📋 进程信息:
    jps -v | findstr %NEW_PID%
    echo.
    echo 📝 日志文件: teaching-api.log
    echo 🔍 实时日志: type teaching-api.log
    echo 🏥 健康检查: curl http://localhost:8081/api/actuator/health
    echo.
    
    :: 等待应用启动
    echo ⏳ 等待应用启动...
    set /a counter=0
    :wait_loop
    set /a counter+=1
    curl -s http://localhost:8081/api/actuator/health >nul 2>&1
    if %errorlevel% equ 0 (
        set /a seconds=counter*10
        echo ✅ 应用启动完成! ^(耗时: %seconds%秒^)
        goto :startup_complete
    )
    if %counter% geq 30 (
        echo ⚠️  应用启动超时，请检查日志: type teaching-api.log
        goto :startup_complete
    )
    echo 等待中... ^(%counter%/30^)
    timeout /t 10 /nobreak >nul
    goto :wait_loop
    
    :startup_complete
    echo.
    echo 🎉 Teaching系统启动完成!
    echo 🌐 访问地址: http://localhost:8081
    echo 📚 API文档: http://localhost:8081/doc.html
) else (
    echo ❌ 启动失败，请检查日志: type teaching-api.log
    pause
    exit /b 1
)

echo.
echo 按任意键退出...
pause >nul
