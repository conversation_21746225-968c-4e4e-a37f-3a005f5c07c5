#!/bin/bash

# Teaching系统 Java 17 启动脚本
# 阶段三：核心框架升级 - Java 17优化启动参数

# 项目配置
PROJECT_NAME="teaching-system"
PROJECT_JAR="jeecg-boot-module-system/target/teaching-open-2.7.0.jar"
PROFILE="dev"  # dev/test/prod

# Java 17优化的JVM启动参数
JAVA_OPTS="
-Xms2048m
-Xmx4096m
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers
-Xlog:gc*:logs/gc.log:time,tags
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=logs/heapdump.hprof
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.time=ALL-UNNAMED
--add-opens java.base/java.net=ALL-UNNAMED
"

# 创建日志目录
mkdir -p logs

# 检查Java版本
echo "🔍 检查Java版本..."
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "当前Java版本: $JAVA_VERSION"

if [[ ! $JAVA_VERSION == 17* ]]; then
    echo "❌ 错误: 需要Java 17，当前版本: $JAVA_VERSION"
    echo "请设置JAVA_HOME指向Java 17安装目录"
    exit 1
fi

# 检查JAR文件是否存在
if [ ! -f "$PROJECT_JAR" ]; then
    echo "❌ 错误: JAR文件不存在: $PROJECT_JAR"
    echo "请先执行: mvn clean package"
    exit 1
fi

# 停止现有进程
echo "🛑 停止现有进程..."
PID=$(jps -l | grep jeecg-boot-module-system | awk '{print $1}')
if [ ! -z "$PID" ]; then
    echo "发现现有进程 PID: $PID，正在停止..."
    kill -9 $PID
    sleep 3
fi

# 启动应用
echo "🚀 启动Teaching系统 (Java 17模式)..."
echo "配置文件: $PROFILE"
echo "JVM参数: $JAVA_OPTS"
echo "JAR文件: $PROJECT_JAR"
echo "启动时间: $(date)"

nohup java $JAVA_OPTS -jar $PROJECT_JAR --spring.profiles.active=$PROFILE > teaching-api.log 2>&1 &

# 获取新进程PID
sleep 5
NEW_PID=$(jps -l | grep jeecg-boot-module-system | awk '{print $1}')

if [ ! -z "$NEW_PID" ]; then
    echo "✅ 启动成功! PID: $NEW_PID"
    echo "📋 进程信息:"
    jps -v | grep $NEW_PID
    echo ""
    echo "📊 内存使用情况:"
    ps -p $NEW_PID -o pid,ppid,pcpu,pmem,vsz,rss,comm
    echo ""
    echo "📝 日志文件: teaching-api.log"
    echo "🔍 实时日志: tail -f teaching-api.log"
    echo "🏥 健康检查: curl http://localhost:8081/api/actuator/health"
    
    # 等待应用启动
    echo "⏳ 等待应用启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8081/api/actuator/health > /dev/null 2>&1; then
            echo "✅ 应用启动完成! (耗时: ${i}0秒)"
            break
        fi
        echo "等待中... (${i}/30)"
        sleep 10
    done
    
    if [ $i -eq 30 ]; then
        echo "⚠️  应用启动超时，请检查日志: tail -f teaching-api.log"
    fi
else
    echo "❌ 启动失败，请检查日志: tail -f teaching-api.log"
    exit 1
fi

echo "🎉 Teaching系统启动完成!"
echo "🌐 访问地址: http://localhost:8081"
echo "📚 API文档: http://localhost:8081/doc.html"
