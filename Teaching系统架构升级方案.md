# Teaching系统架构升级方案（基于真实项目代码）

## 🎯 项目概述

基于对Teaching系统真实代码的深度分析，本方案采用**渐进式、兼容性优先**的升级策略，确保每个阶段都能独立编译、测试、部署和上线运行，最大程度降低升级风险，保证业务连续性。

## 🔑 核心设计原则

### 1. 阶段独立性原则
- 每个阶段必须能够独立编译、运行和上线
- 每个阶段完成后系统功能完整可用
- 后续阶段可以根据业务需要灵活安排时间

### 2. 最小风险原则
- 优先兼容性升级，避免破坏性变更
- 将高风险升级单独隔离为独立阶段
- 每次变更范围最小化

### 3. 快速回滚原则
- 每个阶段都有完整的回滚方案
- 支持快速回退到上一个稳定版本
- 保留多版本部署能力

## 🔍 现状分析（基于真实项目代码）

### 📊 当前技术栈现状（基于真实pom.xml）

#### 后端技术栈
- **Spring Boot**: 2.1.3.RELEASE (2019年发布，已停止支持)
- **Java**: 1.8 (2014年发布，即将停止免费支持)
- **MyBatis-Plus**: 3.1.2 (存在已知漏洞)
- **Shiro**: 1.7.0 (存在认证绕过漏洞)
- **JWT**: 0.9.1 (版本过旧，存在安全漏洞)
- **Druid**: 1.1.17 (连接池配置需要优化)
- **FastJSON**: 1.2.83 (存在RCE高危漏洞)
- **MySQL Connector**: 8.0.28 (版本较旧)
- **Hutool**: 4.5.11 (版本过旧，存在安全漏洞)
- **Log4j2**: 2.17.0 (需要升级到最新版本)

#### 前端技术栈（基于真实package.json）
- **Vue**: 2.x (Vue 2即将停止维护)
- **Ant Design Vue**: 1.6.3 (版本过旧，存在安全漏洞)
- **Axios**: 0.18.0 (存在SSRF漏洞)
- **Webpack**: 4.x (构建性能差)

### 🔍 技术栈过时程度评估

#### 后端技术栈 (风险等级: 🔴 极高)
| 组件 | 当前版本 | 最新版本 | 发布时间差 | 安全风险 | 升级难度 |
|------|----------|----------|------------|----------|----------|
| Spring Boot | 2.1.3.RELEASE | 3.2.2 | 5年 | 🔴 极高 | 🟡 中等 |
| Java | 1.8 | 21 LTS | 9年 | 🟠 高 | 🟡 中等 |
| Shiro | 1.7.0 | 1.13.0 | 3年 | 🔴 极高 | 🟢 低 |
| JWT | 0.9.1 | 4.4.0 | 6年 | 🔴 极高 | 🟢 低 |
| FastJSON | 1.2.83 | 2.0.43 | 2年 | 🔴 极高 | 🟠 高 |
| MyBatis-Plus | 3.1.2 | ******* | 4年 | 🟡 中 | 🟢 低 |
| MySQL Driver | 8.0.28 | 8.2.0 | 2年 | 🟠 高 | 🟢 低 |
| Hutool | 4.5.11 | 5.8.25 | 4年 | 🟠 高 | 🟢 低 |
| Druid | 1.1.17 | 1.2.20 | 3年 | 🟡 中 | 🟢 低 |

#### 前端技术栈 (风险等级: 🟠 高)
| 组件 | 当前版本 | 最新版本 | 发布时间差 | 安全风险 | 升级难度 |
|------|----------|----------|------------|----------|----------|
| Vue | 2.6.10 | 3.4.15 | 4年 | 🟠 高 | 🔴 极高 |
| Ant Design Vue | 1.6.3 | 4.1.2 | 4年 | 🟡 中 | 🔴 极高 |
| Axios | 0.18.0 | 1.6.7 | 5年 | 🔴 极高 | 🟢 低 |
| Vue CLI | 3.3.0 | 5.0.8 | 4年 | 🟠 高 | 🟡 中等 |

### 🏗️ 真实业务架构分析

#### 核心业务模块（基于真实代码）
1. **教学管理模块**
   - `TeachingCourseController` - 课程管理
   - `TeachingMenuController` - 教学菜单管理
   - `TeachingWorkController` - 作品管理

2. **考试系统模块**
   - `ExamRecordServiceImpl` - 考试记录服务
   - 大量使用FastJSON进行JSON解析

3. **判题集成模块**
   - `TeachingJudgeController` - 判题控制器
   - 与云服务器HOJ通信，不是本地判题服务

#### 真实配置分析（基于application.yml）
```yaml
# 真实的Druid连接池配置
spring:
  datasource:
    druid:
      initial-size: 5
      min-idle: 5
      maxActive: 50
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

# 真实的Redis配置
  redis:
    database: 1
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1ms
        min-idle: 0
    shutdown-timeout: 100ms
    password: ''
    port: 6395  # 生产环境

# 真实的Quartz配置
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    auto-startup: true
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
```

## 🎯 升级目标与策略

### 核心目标
1. **🛡️ 安全第一**: 消除所有已知高危漏洞，确保系统安全
2. **⚡ 性能提升**: 响应时间提升50%，并发能力提升3倍
3. **🔧 技术现代化**: 升级到LTS版本，确保5年技术生命周期
4. **🔄 零业务影响**: 所有现有功能和UI保持不变
5. **📊 可观测性**: 建立完善的监控和告警体系
6. **🎯 阶段独立**: 每个阶段完成后都能独立上线运行

### 重新设计的升级策略
1. **兼容性优先**: 优先选择兼容性升级，将破坏性升级单独隔离
2. **渐进式实施**: 大版本升级分步进行，避免跨越太多版本
3. **独立验证**: 每个阶段必须能独立编译、测试、部署、运行
4. **风险隔离**: 高风险变更单独作为独立阶段处理
5. **快速回滚**: 每个阶段都有完整的回滚方案和降级策略

## 🚀 重新设计的分阶段实施方案

### 📋 新阶段概览（确保每阶段独立上线）
```mermaid
gantt
    title Teaching系统渐进式升级时间线
    dateFormat  YYYY-MM-DD
    section 阶段一：安全补丁升级
    兼容性安全升级  :crit, a1, 2024-01-01, 10d
    安全配置强化    :crit, a2, after a1, 4d
    section 阶段二：Spring Boot渐进升级
    升级到2.3.x    :active, b1, after a2, 14d
    稳定性测试     :b2, after b1, 7d
    section 阶段三：核心框架升级
    Spring Boot 2.7 :c1, after b2, 14d
    Java 17配套升级 :c2, after c1, 7d
    section 阶段四：破坏性依赖升级
    FastJSON 2.x升级:d1, after c2, 14d
    兼容性适配     :d2, after d1, 7d
    section 阶段五：前端现代化
    Vue 3兼容模式   :e1, after d2, 14d
    组件逐步迁移    :e2, after e1, 21d
    section 阶段六：性能优化与运维
    性能调优       :f1, after e2, 10d
    监控体系建设    :f2, after f1, 7d
```

### 🎯 阶段独立性设计说明

每个阶段完成后都能：
- ✅ **独立编译**：无编译错误，依赖完整
- ✅ **独立测试**：通过完整的单元测试和集成测试
- ✅ **独立部署**：可以正常启动和运行
- ✅ **独立上线**：所有业务功能正常，用户无感知
- ✅ **快速回滚**：出现问题可以快速回退到上一版本

---

## 🔥 阶段一：安全补丁升级 (优先级: 🔴 极高) ✅ **可独立上线**

**⏱️ 时间**: 2周 | **👥 人员**: 2名高级开发 | **🎯 目标**: 消除高危漏洞，确保系统稳定运行

### 🎯 阶段目标
- 修复已知安全漏洞，但避免破坏性升级
- 确保升级后系统功能完整，可以独立上线运行
- 为后续阶段升级奠定安全基础

### 1.1 兼容性安全依赖升级（基于真实pom.xml）

#### 🛡️ Shiro认证绕过漏洞修复（兼容性升级）
```xml
<dependency>
    <groupId>org.apache.shiro</groupId>
    <artifactId>shiro-spring-boot-starter</artifactId>
    <version>1.13.0</version> <!-- 从1.7.0升级，兼容性升级 -->
</dependency>
```

#### 🔒 MySQL驱动安全升级（兼容性升级）
```xml
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <version>8.2.0</version> <!-- 从8.0.28升级，注意artifactId变更 -->
</dependency>
```

#### 📝 Log4j2安全升级（兼容性升级）
```xml
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-core</artifactId>
    <version>2.22.1</version> <!-- 从2.17.0升级，修复Log4Shell漏洞 -->
</dependency>
```

#### 🔧 Hutool工具类升级（兼容性升级）
```xml
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-all</artifactId>
    <version>5.8.25</version> <!-- 从4.5.11升级，兼容性升级 -->
</dependency>
```

#### 🌐 前端Axios安全升级（兼容性升级）
```json
{
  "dependencies": {
    "axios": "^1.6.7" // 从0.18.0升级，修复SSRF漏洞
  }
}
```

#### ⚠️ FastJSON漏洞临时缓解方案
**重要决策**: FastJSON 1.x→2.x是破坏性升级，**不在此阶段进行**，采用临时缓解方案：
```java
// 在Application启动类中添加安全配置
@SpringBootApplication
public class JeecgApplication {
    static {
        // 禁用FastJSON危险特性
        System.setProperty("fastjson.parser.autoTypeSupport", "false");
        System.setProperty("fastjson.parser.safeMode", "true");
    }

    public static void main(String[] args) {
        SpringApplication.run(JeecgApplication.class, args);
    }
}
```
**说明**: FastJSON破坏性升级将在阶段四单独处理，确保充分测试。

### 1.2 安全配置强化（基于真实配置）

#### 🔐 JWT安全配置加强（基于真实业务代码）
```java
// 在现有的JwtUtil类中优化配置
public class JwtUtil {
    // 使用更强的密钥长度
    private static final String SECRET_KEY = generateSecureKey(); // 256位密钥

    // Token过期时间缩短（渐进式调整）
    public static final long EXPIRE_TIME = 4 * 60 * 60; // 4小时，从默认时间缩短

    // 添加Token刷新机制
    public static final long REFRESH_TIME = 30 * 60; // 30分钟内可刷新

    // 使用更安全的算法
    Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
}
```

#### 🛡️ XSS防护加强（基于真实前端代码）
```javascript
// 在现有的前端工具类中添加XSS防护
// web/src/utils/security.js
import DOMPurify from 'dompurify'

export function sanitizeHtml(dirty) {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li'],
    ALLOWED_ATTR: ['class', 'style']
  })
}

// 在所有富文本显示处使用
<div v-html="sanitizeHtml(content)"></div>
```

### 1.3 阶段一验收标准

#### ✅ 独立上线验收清单
- [ ] **编译测试**: 项目能够正常编译，无依赖冲突
- [ ] **启动测试**: 应用能够正常启动，所有Bean加载成功
- [ ] **功能测试**: 所有现有功能正常运行，无功能缺失
- [ ] **安全测试**: 已知高危漏洞得到修复或缓解
- [ ] **性能测试**: 性能指标不低于升级前水平
- [ ] **回滚测试**: 验证快速回滚方案可行性

#### 🔍 安全扫描验证
```xml
<!-- Maven安全扫描插件 -->
<plugin>
    <groupId>org.owasp</groupId>
    <artifactId>dependency-check-maven</artifactId>
    <version>9.0.7</version>
    <configuration>
        <failBuildOnCVSS>7</failBuildOnCVSS>
    </configuration>
</plugin>
```

#### 🚀 部署和回滚方案
```bash
# 部署脚本
#!/bin/bash
echo "开始部署阶段一升级版本..."
# 备份当前版本
cp jeecg-boot-module-system-2.1.4.jar jeecg-boot-module-system-2.1.4.jar.backup
# 部署新版本
java -jar jeecg-boot-module-system-2.1.4-phase1.jar --spring.profiles.active=prod &
echo "等待服务启动..."
sleep 30
# 健康检查
curl -f http://localhost:8081/actuator/health || exit 1
echo "阶段一部署成功！"

# 回滚脚本
#!/bin/bash
echo "开始回滚到原始版本..."
pkill -f jeecg-boot-module-system
java -jar jeecg-boot-module-system-2.1.4.jar.backup --spring.profiles.active=prod &
echo "回滚完成！"
```

---

## ⚡ 阶段二：Spring Boot渐进升级 (优先级: 🟠 高) ✅ **可独立上线**

**⏱️ 时间**: 3周 | **👥 人员**: 2名开发 | **🎯 目标**: Spring Boot 2.1.3 → 2.3.12 (稳定中间版本)

### 🎯 阶段目标
- 将Spring Boot升级到稳定的中间版本2.3.12
- 确保在Java 8环境下稳定运行
- 为后续Java 17升级做好准备
- 升级后系统功能完整，可以独立上线运行

### 2.1 渐进式升级策略

#### 📋 升级路径规划（降低风险）
```
2.1.3 → 2.3.12.RELEASE (稳定LTS版本，Java 8兼容)
```
**策略说明**:
- 选择2.3.12作为中间版本，该版本稳定且与Java 8完全兼容
- 避免一次性跨越太多版本，降低升级风险
- 为阶段三的Java 17升级奠定基础

#### 🔧 POM文件升级（基于真实pom.xml）
```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.3.12.RELEASE</version> <!-- 稳定中间版本，Java 8兼容 -->
    <relativePath/>
</parent>

<!-- 添加属性迁移工具 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-properties-migrator</artifactId>
    <scope>runtime</scope>
</dependency>
```

### 2.2 依赖版本同步升级（基于真实项目，Java 8兼容）

#### 📦 核心依赖版本表
```xml
<properties>
    <java.version>1.8</java.version> <!-- 保持Java 8，阶段三再升级 -->
    <mybatis-plus.version>3.4.3.4</mybatis-plus.version> <!-- 兼容Spring Boot 2.3 -->
    <druid.version>1.1.17</druid.version> <!-- 保持当前版本 -->
    <jwt.version>0.9.1</jwt.version> <!-- 保持当前版本 -->
    <swagger.version>2.9.2</swagger.version> <!-- 保持2.x版本，避免破坏性升级 -->
    <hutool.version>5.8.25</hutool.version> <!-- 已在阶段一升级 -->
</properties>
```

#### 📚 保持Swagger 2.x配置（避免破坏性升级）
```java
// 保持现有Swagger 2.x配置不变
@EnableSwagger2
@Configuration
public class SwaggerConfig {
    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .select()
            .apis(RequestHandlerSelectors.basePackage("org.jeecg.modules"))
            .paths(PathSelectors.any())
            .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("Teaching系统API文档")
            .description("Teaching系统接口文档")
            .version("1.0")
            .build();
    }
}
```
**说明**: Swagger 3.x升级将在后续阶段进行，此阶段保持稳定。

### 2.3 配置文件适配（基于真实application.yml）

#### ⚙️ application.yml 适配（Spring Boot 2.3.x）
```yaml
# Spring Boot 2.3.x 配置适配
spring:
  # 保持现有配置，最小变更
  application:
    name: teaching-system

  # 数据源配置保持不变（基于真实配置）
  datasource:
    druid:
      initial-size: 5
      min-idle: 5
      maxActive: 50
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # 文件上传配置适配（2.3版本变更）
  servlet:
    multipart:
      max-file-size: 1000MB  # 保持现有配置
      max-request-size: 1000MB  # 保持现有配置

# 管理端点配置（保守配置）
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: when-authorized
```

#### 🔧 最小化配置变更原则
- 保持现有配置结构不变
- 只修改必要的兼容性配置
- 避免引入新特性配置

### 2.4 代码适配修改（基于真实业务代码）

#### 🔧 WebMvcConfigurer适配（Spring Boot 2.3.x兼容）
```java
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    // Spring Boot 2.3.x 跨域配置（保持现有方式）
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOrigins("*") // 2.3.x版本仍支持allowedOrigins
            .allowCredentials(true)
            .allowedMethods("*")
            .allowedHeaders("*");
    }

    // 保持现有的其他配置不变
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 现有静态资源配置保持不变
        super.addResourceHandlers(registry);
    }
}
```

### 2.5 阶段二验收标准

#### ✅ 独立上线验收清单
- [ ] **编译测试**: Spring Boot 2.3.12版本正常编译
- [ ] **启动测试**: 应用在Java 8环境下正常启动
- [ ] **功能测试**: 所有API接口正常响应
- [ ] **兼容性测试**: 与现有前端完全兼容
- [ ] **性能测试**: 性能指标不低于升级前
- [ ] **稳定性测试**: 连续运行24小时无异常

#### 🎯 关键业务功能验证（基于真实业务代码）
```java
// 验证核心业务功能正常
@Test
public void testTeachingCourseController() {
    // 测试课程列表查询
    Result<?> result = teachingCourseController.queryPageList(
        new TeachingCourse(), 1, 10, mockRequest);
    Assert.assertTrue(result.isSuccess());

    // 测试课程添加
    TeachingCourse course = new TeachingCourse();
    course.setCourseName("测试课程");
    Result<?> addResult = teachingCourseController.add(course);
    Assert.assertTrue(addResult.isSuccess());
}

@Test
public void testFastJsonCompatibility() {
    // 验证FastJSON功能正常
    String json = "{\"name\":\"test\",\"value\":123}";
    JSONObject obj = JSON.parseObject(json);
    Assert.assertEquals("test", obj.getString("name"));
    Assert.assertEquals(123, obj.getInteger("value"));
}
```

---

## ☕ 阶段三：核心框架升级 (优先级: 🟡 中等) ✅ **可独立上线**

**⏱️ 时间**: 3周 | **👥 人员**: 2名开发 | **🎯 目标**: Spring Boot 2.7.18 + Java 17 配套升级

### 🎯 阶段目标
- Spring Boot 2.3.12 → 2.7.18 LTS
- Java 8 → Java 17 LTS（配套升级）
- 两个升级必须同时进行，确保兼容性
- 升级后系统功能完整，可以独立上线运行

### 3.1 配套升级策略

#### 🎯 升级收益分析
- **Spring Boot 2.7.18**: LTS版本，支持到2025年
- **Java 17**: LTS版本，支持到2029年
- **性能提升**: GC性能提升15-20%，Spring Boot性能优化
- **安全增强**: 更强的安全特性和漏洞修复
- **语言特性**: Records、Pattern Matching、Text Blocks等现代Java特性

#### 🔧 POM文件配套升级
```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version> <!-- LTS版本，支持到2025年 -->
    <relativePath/>
</parent>

<properties>
    <java.version>17</java.version> <!-- 从1.8升级到17 -->
    <mybatis-plus.version>*******</mybatis-plus.version> <!-- 支持Java 17 -->
    <druid.version>1.2.20</druid.version>
    <jwt.version>4.4.0</jwt.version> <!-- Java 17兼容版本 -->
    <hutool.version>5.8.25</hutool.version>
</properties>
```

#### 🔍 兼容性检查和迁移
```bash
# 使用jdeps检查依赖兼容性
jdeps --jdk-internals --multi-release 17 target/classes

# 使用OpenRewrite自动化迁移
./mvnw org.openrewrite.maven:rewrite-maven-plugin:run \
  -Drewrite.recipeArtifactCoordinates=org.openrewrite.recipe:rewrite-migrate-java:LATEST \
  -Drewrite.activeRecipes=org.openrewrite.java.migrate.Java8toJava17
```

### 3.2 Spring Boot 2.7配置适配

#### ⚙️ application.yml 配置更新
```yaml
spring:
  # 2.6+版本新增的循环依赖检查
  main:
    allow-circular-references: true

  # 2.4+版本路径匹配策略变更
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # 2.6+版本Actuator端点变更
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
  endpoint:
    health:
      show-details: when-authorized
```

### 3.3 JVM参数优化（基于真实部署）

#### 🚀 生产环境JVM配置（Java 17优化）
```bash
# Java 17优化的JVM启动参数（基于真实start-teaching.sh）
JAVA_OPTS="
-Xms2048m
-Xmx4096m
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers
-Xlog:gc*:logs/gc.log:time,tags
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=logs/heapdump.hprof
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
"

# 更新start-teaching.sh脚本
nohup java $JAVA_OPTS -jar $PROJECT_JAR --spring.profiles.active=prod > teaching-api.log&
```

### 3.4 代码现代化改造（基于真实业务代码）

#### 🆕 使用Java 17新特性（可选，不影响功能）
```java
// 可选：在新增功能时使用Records简化数据类
public record CourseInfo(String id, String name, String description) {}

// 可选：使用Text Blocks改善SQL可读性（在Mapper中）
@Select("""
    SELECT c.id, c.course_name, c.description
    FROM teaching_course c
    WHERE c.del_flag = 0
    AND c.create_time > #{startTime}
    ORDER BY c.create_time DESC
    """)
List<TeachingCourse> selectRecentCourses(@Param("startTime") Date startTime);

// 可选：使用Pattern Matching简化instanceof（在Service中）
public String formatCourseData(Object data) {
    if (data instanceof String str && str.length() > 0) {
        return str.toUpperCase();
    } else if (data instanceof Number num) {
        return num.toString();
    }
    return "未知数据";
}

// 可选：使用Switch Expressions（在Controller中）
public String getCourseStatus(Integer status) {
    return switch (status) {
        case 0 -> "草稿";
        case 1 -> "已发布";
        case 2 -> "已下线";
        default -> "未知状态";
    };
}
```

### 3.5 阶段三验收标准

#### ✅ 独立上线验收清单
- [ ] **编译测试**: Java 17 + Spring Boot 2.7.18正常编译
- [ ] **启动测试**: 应用在Java 17环境下正常启动
- [ ] **功能测试**: 所有业务功能正常运行
- [ ] **性能测试**: GC性能提升，响应时间改善
- [ ] **兼容性测试**: 与现有前端和数据库完全兼容
- [ ] **稳定性测试**: 连续运行48小时无异常

#### 🎯 关键业务功能验证（基于真实业务）
```java
// 验证判题服务正常（基于真实TeachingJudgeController）
@Test
public void testJudgeServiceCompatibility() {
    TestJudgeRequestDTO request = new TestJudgeRequestDTO();
    request.setCode("print('Hello World')");
    request.setLanguage("python");

    Result<?> result = teachingJudgeController.testJudge(request);
    Assert.assertTrue(result.isSuccess());
}

// 验证FastJSON在Java 17下正常工作
@Test
public void testFastJsonJava17() {
    // 基于真实ExamRecordServiceImpl中的JSON处理
    String paperContent = "{\"questions\":[{\"questionId\":\"123\",\"score\":10}]}";
    JSONObject paperObj = JSON.parseObject(paperContent);
    JSONArray questions = paperObj.getJSONArray("questions");
    Assert.assertNotNull(questions);
    Assert.assertEquals(1, questions.size());
}
```

---

## 🔥 阶段四：破坏性依赖升级 (优先级: 🟠 高) ✅ **可独立上线**

**⏱️ 时间**: 3周 | **👥 人员**: 2名开发 | **🎯 目标**: 处理所有破坏性升级（FastJSON、Swagger等）

### 🎯 阶段目标
- 处理FastJSON 1.x → 2.x破坏性升级
- 升级Swagger 2.x → 3.x
- 处理其他破坏性依赖升级
- 确保升级后系统功能完整，可以独立上线运行

### 4.1 FastJSON破坏性升级（基于真实业务代码）

#### 🚨 FastJSON 1.x → 2.x升级
```xml
<!-- pom.xml 修改 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson2</artifactId>
    <version>2.0.43</version> <!-- 从1.2.83升级，使用fastjson2 -->
</dependency>
```

#### 🔧 兼容性封装类（基于真实业务需求）
```java
// 创建兼容性工具类，统一JSON处理
@Component
public class JsonUtils {

    /**
     * 对象转JSON字符串
     */
    public static String toJSONString(Object object) {
        return JSON.toJSONString(object);
    }

    /**
     * JSON字符串转对象
     */
    public static <T> T parseObject(String text, Class<T> clazz) {
        return JSON.parseObject(text, clazz);
    }

    /**
     * JSON字符串转List
     */
    public static <T> List<T> parseArray(String text, Class<T> clazz) {
        return JSON.parseArray(text, clazz);
    }

    /**
     * 兼容旧版本数据格式（基于ExamRecordServiceImpl的实际使用）
     */
    public static JSONObject parseObject(String text) {
        return JSON.parseObject(text);
    }
}
```

#### 🔄 业务代码适配（基于真实ExamRecordServiceImpl）
```java
// 在ExamRecordServiceImpl中的实际应用
@Override
public Result<?> getPaperDetail(String paperId) {
    // 查询试卷信息
    ExamPaper paper = examPaperService.getById(paperId);
    if (paper == null) {
        return Result.error("试卷不存在");
    }

    // 使用新的JSON解析方式
    JSONObject paperContent = JsonUtils.parseObject(paper.getContent());
    List<Map<String, Object>> questions = new ArrayList<>();
    paperContent.getJSONArray("questions").forEach(item -> {
        @SuppressWarnings("unchecked")
        Map<String, Object> questionMap = (Map<String, Object>) item;
        questions.add(questionMap);
    });

    // 其余业务逻辑保持不变
    return Result.ok(questions);
}
```

### 4.2 Swagger 2.x → 3.x升级

#### 📚 Swagger升级配置
```xml
<!-- 移除旧版本Swagger -->
<!--
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger2</artifactId>
</dependency>
-->

<!-- 添加新版本Swagger -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.7.0</version>
</dependency>
```

#### 🔧 配置类更新
```java
// 新的Swagger 3.x配置
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Teaching系统API文档")
                .version("2.0")
                .description("Teaching系统接口文档"))
            .servers(List.of(
                new Server().url("http://localhost:8081").description("开发环境"),
                new Server().url("https://api.teaching.com").description("生产环境")
            ));
    }
}
```

### 4.3 阶段四验收标准

#### ✅ 独立上线验收清单
- [ ] **JSON处理**: FastJSON 2.x正常工作，数据序列化无问题
- [ ] **API文档**: Swagger 3.x正常显示，接口文档完整
- [ ] **功能测试**: 所有业务功能正常，无JSON相关错误
- [ ] **兼容性测试**: 新旧数据格式兼容
- [ ] **性能测试**: JSON处理性能不低于升级前

---

## 🎨 阶段五：前端现代化升级 (优先级: 🟡 中等) ✅ **可独立上线**

**⏱️ 时间**: 5周 | **👥 人员**: 2名前端开发 | **🎯 目标**: Vue 2.x → Vue 3.x

### 🎯 阶段目标
- Vue 2.6.10 → Vue 3.x渐进式升级
- 使用兼容模式确保平滑过渡
- 升级后系统功能完整，可以独立上线运行
- 前端升级相对独立，不影响后端

### 5.1 Vue 3升级策略（基于真实前端代码）

#### 🛣️ 渐进式升级路径
```
Vue 2.6.10 → Vue 2.7.x (过渡版本) → Vue 3.x + 兼容模式 → 纯Vue 3.x
```

#### 📦 依赖升级计划（基于真实package.json）
```json
{
  "dependencies": {
    "vue": "^3.4.15",
    "@vue/compat": "^3.4.15", // 兼容模式
    "ant-design-vue": "^4.1.2",
    "vue-router": "^4.2.5",
    "vuex": "^4.1.0", // 或迁移到Pinia
    "axios": "^1.6.7" // 已在阶段一升级
  },
  "devDependencies": {
    "@vue/cli-service": "^5.0.8",
    "@vue/compiler-sfc": "^3.4.15"
  }
}
```

### 5.2 兼容性处理

#### 🔧 Vue 3兼容模式配置
```javascript
// vue.config.js
module.exports = {
  chainWebpack: config => {
    config.resolve.alias.set('vue', '@vue/compat')

    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => {
        return {
          ...options,
          compilerOptions: {
            compatConfig: {
              MODE: 2 // Vue 2兼容模式
            }
          }
        }
      })
  }
}
```

### 5.3 阶段五验收标准

#### ✅ 独立上线验收清单
- [ ] **编译测试**: Vue 3项目正常编译打包
- [ ] **功能测试**: 所有页面功能正常，无Vue相关错误
- [ ] **兼容性测试**: 与后端API完全兼容
- [ ] **用户体验**: UI/UX保持一致，无用户感知变化
- [ ] **浏览器兼容**: 主流浏览器正常运行
- [ ] **性能测试**: 前端性能不低于升级前

---

## 🚀 阶段六：性能优化与运维体系 (优先级: 🟢 低) ✅ **可独立上线**

**⏱️ 时间**: 3周 | **👥 人员**: 1名开发+1名运维 | **🎯 目标**: 性能优化和监控体系建设

### 🎯 阶段目标
- 基于真实配置的数据库连接池和缓存优化
- JVM参数精细调优
- 建立完善的监控告警体系
- 升级后系统性能显著提升，可以独立上线运行

### 6.1 数据库性能优化（基于真实application.yml）

#### 🗄️ 连接池配置优化
```yaml
spring:
  datasource:
    druid:
      # 连接池基础配置优化
      initial-size: 20          # 从5提升到20
      min-idle: 20              # 从5提升到20
      maxActive: 200            # 从50提升到200
      maxWait: 60000            # 保持现有配置

      # 连接检测配置（保持现有）
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false

      # 性能监控配置（保持现有）
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall,slf4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000

      # 新增：连接泄露检测
      removeAbandoned: true
      removeAbandonedTimeout: 1800
      logAbandoned: true
```

#### 📊 Redis性能优化（基于真实配置）
```yaml
spring:
  redis:
    database: 1  # 保持现有配置
    host: 127.0.0.1
    port: 6395  # 保持生产环境配置
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 50        # 从8提升到50
        max-idle: 20          # 从8提升到20
        min-idle: 10          # 新增最小空闲连接
        max-wait: 3000ms      # 从-1ms改为3秒
      shutdown-timeout: 100ms
```

### 6.2 基于真实配置的监控优化

#### 📈 优化现有Actuator配置（基于真实项目）
```yaml
# 项目已有spring-boot-starter-actuator依赖，优化现有配置
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"  # 适度扩展监控端点
  endpoint:
    health:
      show-details: when-authorized  # 与真实配置保持一致
```

#### 🔧 优化现有日志配置（基于真实application.yml）
```yaml
# 基于真实application-prod.yml的日志优化
logging:
  level:
    root: WARN  # 保持现有配置
    org.jeecg: INFO  # 从WARN提升到INFO，便于监控
    org.jeecg.modules.system.mapper: INFO
    org.jeecg.modules.teaching.mapper: INFO
    org.quartz: WARN  # 保持现有配置
  file:
    max-size: 100MB  # 保持现有配置
    max-history: 30  # 保持现有配置
```

### 6.3 基于真实代码的性能优化

#### ⚡ 优化现有Quartz定时任务配置（基于真实application.yml）
```yaml
# 基于真实项目的Quartz配置优化
spring:
  quartz:
    job-store-type: jdbc  # 保持现有配置
    initialize-schema: embedded
    auto-startup: true
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 20  # 从10提升到20，提高并发处理能力
            threadPriority: 5
```

#### 🔧 基于真实业务的代码优化建议
```java
// 在现有的TeachingCourseController中添加性能监控
@RestController
@RequestMapping("/teaching/teachingCourse")
@Slf4j
public class TeachingCourseController extends JeecgController<TeachingCourse, ITeachingCourseService> {

    @GetMapping("/list")
    @PermissionData
    public Result<?> queryPageList(TeachingCourse teachingCourse,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        long startTime = System.currentTimeMillis();
        try {
            // 保持现有业务逻辑不变
            Map<String, String[]> param = req.getParameterMap();
            QueryWrapper<TeachingCourse> queryWrapper = new QueryWrapper<>();

            // 现有的部门权限过滤逻辑保持不变
            if (param.containsKey("departId")){
                List<String> parentDepIds = sysDepartService.getParentDepartIds(param.get("departId")[0]);
                queryWrapper.and(wrapper -> {
                    wrapper.or().eq("depart_ids","");
                    for (String departId : parentDepIds){wrapper.or().like("depart_ids", departId);}
                    return wrapper;
                });
            }

            QueryGenerator.installMplus(queryWrapper, teachingCourse, req.getParameterMap());
            Page<TeachingCourse> page = new Page<TeachingCourse>(pageNo, pageSize);
            IPage<TeachingCourse> pageList = teachingCourseService.page(page, queryWrapper);

            long duration = System.currentTimeMillis() - startTime;
            log.info("课程列表查询完成，耗时: {}ms, 页码: {}, 页大小: {}, 结果数: {}",
                    duration, pageNo, pageSize, pageList.getRecords().size());

            return Result.ok(pageList);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("课程列表查询失败，耗时: {}ms, 错误: {}", duration, e.getMessage(), e);
            throw e;
        }
    }
}
```

### 6.4 阶段六验收标准

#### ✅ 独立上线验收清单
- [ ] **性能指标**: 响应时间提升50%，并发能力提升3倍
- [ ] **监控完整**: 关键业务指标全面监控
- [ ] **告警正常**: 告警机制正常工作
- [ ] **稳定运行**: 优化后系统稳定运行
- [ ] **资源利用**: CPU、内存、数据库连接池利用率合理

---

## 📅 重新设计的实施计划

### 🗓️ 时间线总览 (17周) - 确保每阶段独立上线

| 阶段 | 时间 | 里程碑 | 交付物 | 独立上线 |
|------|------|--------|--------|----------|
| 阶段一 | 第1-2周 | 安全补丁升级完成 | 兼容性安全修复报告 | ✅ 可上线 |
| 阶段二 | 第3-5周 | Spring Boot渐进升级完成 | Spring Boot 2.3.12稳定运行 | ✅ 可上线 |
| 阶段三 | 第6-8周 | 核心框架升级完成 | Spring Boot 2.7.18 + Java 17 | ✅ 可上线 |
| 阶段四 | 第9-11周 | 破坏性依赖升级完成 | FastJSON 2.x + Swagger 3.x | ✅ 可上线 |
| 阶段五 | 第12-16周 | 前端现代化完成 | Vue 3全面迁移 | ✅ 可上线 |
| 阶段六 | 第17周 | 性能优化与运维完成 | 监控告警系统 | ✅ 可上线 |

### 🎯 关键设计改进

#### 1. 阶段独立性保证
- 每个阶段完成后都能独立编译、测试、部署、上线
- 后续阶段可以根据业务需要灵活安排时间
- 每个阶段都有完整的回滚方案

#### 2. 风险控制策略
- 将高风险的破坏性升级（FastJSON、Vue 3）单独隔离
- 采用渐进式升级，避免一次性跨越太多版本
- 优先兼容性升级，最小化变更范围

#### 3. 基于真实项目的实用性
- 所有配置示例都基于真实的application.yml
- 所有代码示例都基于真实的Controller/Service
- 所有版本信息都与真实pom.xml一致

### 🛠️ 每阶段实施要点

#### 阶段一：安全补丁升级
- **关键**: 避免破坏性升级，采用兼容性安全修复
- **验证**: 所有现有功能正常，无安全漏洞
- **回滚**: 5分钟内完成版本回退

#### 阶段二：Spring Boot渐进升级
- **关键**: 选择稳定的中间版本2.3.12
- **验证**: 在Java 8环境下稳定运行
- **回滚**: 保留原版本jar包，快速切换

#### 阶段三：核心框架升级
- **关键**: Spring Boot 2.7 + Java 17必须配套升级
- **验证**: 性能提升，GC优化效果明显
- **回滚**: 同时回退Java版本和Spring Boot版本

#### 阶段四：破坏性依赖升级
- **关键**: 充分测试FastJSON 2.x兼容性
- **验证**: 所有JSON处理功能正常
- **回滚**: 快速切换到FastJSON 1.x版本

#### 阶段五：前端现代化
- **关键**: 使用Vue 3兼容模式平滑过渡
- **验证**: 所有页面功能正常，用户无感知
- **回滚**: 前端独立回滚，不影响后端

#### 阶段六：性能优化与运维
- **关键**: 基于真实配置进行优化
- **验证**: 性能指标显著提升
- **回滚**: 配置文件级别的快速回滚

## 📝 总结与建议

### 🎯 重新设计方案的核心价值

本次Teaching系统架构升级方案重新设计后，具有以下核心优势：

#### 1. 🎯 阶段独立性保证
- **每个阶段都能独立上线运行**，避免了原方案中的依赖风险
- **业务连续性保障**，升级过程中系统始终可用
- **灵活的时间安排**，可以根据业务需要调整后续阶段的实施时间

#### 2. 🛡️ 风险控制优化
- **破坏性升级隔离**，将FastJSON、Vue 3等高风险升级单独处理
- **渐进式升级策略**，避免一次性跨越太多版本
- **兼容性优先原则**，最大程度保证系统稳定性

#### 3. 📈 基于真实项目的实用性
- **🛡️ 安全价值**: 消除所有已知高危漏洞，提升系统安全等级
- **⚡ 性能价值**: 响应速度提升50%，并发能力提升300%
- **🔧 技术价值**: 技术栈现代化，确保5年技术生命周期
- **💰 商业价值**: 降低安全风险，提升用户体验，减少维护成本

### 🚀 架构师实施建议

#### 1. 阶段执行策略
- **严格按阶段执行**: 每个阶段完成并验收后再进行下一阶段
- **独立性验证**: 每个阶段都要进行完整的编译、测试、部署验证
- **业务优先**: 可以根据业务需要暂停升级，当前阶段版本稳定运行

#### 2. 风险控制措施
- **分支管理**: 每个阶段使用独立的feature分支
- **环境隔离**: 准备完整的测试环境进行验证
- **回滚预案**: 每个阶段都要有5分钟内快速回滚的能力
- **监控告警**: 升级后加强系统监控，及时发现问题

#### 3. 质量保证体系
- **自动化测试**: 每个阶段都要有完整的自动化测试覆盖
- **性能基准**: 建立性能基准，确保升级后性能不降级
- **兼容性测试**: 重点测试API兼容性和数据格式兼容性

### ⚠️ 关键注意事项

#### 1. 数据安全保障
- **完整备份**: 每个阶段升级前都要做完整的数据库备份
- **增量备份**: 升级过程中保持增量备份
- **备份验证**: 定期验证备份数据的完整性和可恢复性

#### 2. 业务连续性保证
- **低峰期升级**: 选择业务低峰期进行升级操作
- **灰度发布**: 生产环境采用灰度发布策略
- **用户通知**: 提前通知用户可能的服务影响

#### 3. 应急响应预案
- **快速回滚**: 5分钟内完成版本回滚
- **问题定位**: 建立问题快速定位机制
- **应急联系**: 建立24小时应急联系机制

### 🎉 预期成果

#### 升级完成后的系统特性
- **🛡️ 安全可靠**: 消除所有已知高危漏洞的现代化教学平台
- **⚡ 高性能**: 响应速度提升50%的在线考试系统
- **🔧 易维护**: 基于LTS版本的稳定技术架构
- **📈 可扩展**: 支持未来5年发展的业务平台
- **🎯 独立部署**: 每个阶段都能独立运行的灵活架构

#### 长期价值
这个重新设计的升级方案将为Teaching系统带来：
1. **技术债务清零**: 彻底解决历史技术债务问题
2. **安全风险消除**: 建立现代化的安全防护体系
3. **性能显著提升**: 为用户提供更好的使用体验
4. **维护成本降低**: 基于LTS版本的长期稳定架构
5. **团队能力提升**: 掌握现代化技术栈的开发团队

### 🏆 方案优势总结

相比原方案，重新设计的方案具有以下显著优势：
- ✅ **每个阶段都能独立上线**，避免了升级风险
- ✅ **业务连续性保障**，升级过程不影响正常使用
- ✅ **风险可控**，破坏性升级单独隔离处理
- ✅ **时间灵活**，可以根据业务需要调整进度
- ✅ **回滚简单**，每个阶段都有快速回滚能力
- ✅ **基于真实项目**，所有方案都可以直接应用

---

**📞 联系方式**: 如有任何技术问题或实施疑问，请及时沟通
**📅 更新日期**: 2024年1月
**📋 版本**: v2.0 (基于真实项目代码重新设计版)
**👨‍💻 设计理念**: 阶段独立、风险可控、业务连续、基于真实代码
```
```
